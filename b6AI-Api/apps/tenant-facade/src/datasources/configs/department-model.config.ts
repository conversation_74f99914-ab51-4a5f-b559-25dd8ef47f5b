import {ModifiedRestService} from '@sourceloop/core';

// Department Model (copied from agent-portal-service)
export interface Department {
  id?: string;
  name: string;
  status: string;
  createdOn?: string;
  modifiedOn?: string;
  deleted?: boolean;
  deletedOn?: string;
  deletedBy?: string;
}

// Proxy Type
export interface DepartmentProxyType {
  getDepartments(token: string): Promise<Department[]>;
  getActiveDepartments(token: string): Promise<Department[]>;
  getDepartmentById(id: string, token: string): Promise<Department>;
  createDepartment(
    body: Omit<Department, 'id'>,
    token: string,
  ): Promise<Department>;
  updateDepartment(
    id: string,
    body: Partial<Department>,
    token: string,
  ): Promise<void>;
  deleteDepartment(id: string, token: string): Promise<void>;
}

// Proxy Configuration
export const departmentProxyConfig = [
  {
    template: {
      method: 'GET',
      url: '/departments',
      headers: {
        accept: 'application/json',
        Authorization: '{token}',
      },
    },
    functions: {
      getDepartments: ['token'],
    },
  },
  {
    template: {
      method: 'GET',
      url: '/departments/active',
      headers: {
        accept: 'application/json',
        Authorization: '{token}',
      },
    },
    functions: {
      getActiveDepartments: ['token'],
    },
  },
  {
    template: {
      method: 'GET',
      url: '/departments/{id}',
      headers: {
        accept: 'application/json',
        Authorization: '{token}',
      },
    },
    functions: {
      getDepartmentById: ['id', 'token'],
    },
  },
  {
    template: {
      method: 'POST',
      url: '/departments',
      headers: {
        accept: 'application/json',
        'content-type': 'application/json',
        Authorization: '{token}',
      },
      body: '{body}',
    },
    functions: {
      createDepartment: ['body', 'token'],
    },
  },
  {
    template: {
      method: 'PATCH',
      url: '/departments/{id}',
      headers: {
        accept: 'application/json',
        'content-type': 'application/json',
        Authorization: '{token}',
      },
      body: '{body}',
    },
    functions: {
      updateDepartment: ['id', 'body', 'token'],
    },
  },
  {
    template: {
      method: 'DELETE',
      url: '/departments/{id}',
      headers: {
        accept: 'application/json',
        Authorization: '{token}',
      },
    },
    functions: {
      deleteDepartment: ['id', 'token'],
    },
  },
];
