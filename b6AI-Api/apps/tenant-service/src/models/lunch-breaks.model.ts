import {model, property, belongsTo} from '@loopback/repository';
import {SoftDeleteEntity} from '@b6ai/core';
import {BusinessHours} from './business-hours.model';

@model({name: 'lunch_breaks'})
export class LunchBreaks extends SoftDeleteEntity {
  @property({
    type: 'string',
    id: true,
    generated: true,
  })
  id?: string;

  @property({
    type: 'string',
    required: true,
    name: 'start_time',
    jsonSchema: {
      format: 'time',
      description: 'Lunch start time in HH:MM format (e.g., "12:00")',
    },
  })
  startTime: string;

  @property({
    type: 'string',
    required: true,
    name: 'end_time',
    jsonSchema: {
      format: 'time',
      description: 'Lunch end time in HH:MM format (e.g., "13:00")',
    },
  })
  endTime: string;

  @belongsTo(() => BusinessHours, {name: 'businessHours'})
  businessHoursId: string;

  constructor(data?: Partial<LunchBreaks>) {
    super(data);
  }
}

export interface LunchBreaksRelations {
  businessHours?: BusinessHours;
}

export type LunchBreaksWithRelations = LunchBreaks & LunchBreaksRelations;
