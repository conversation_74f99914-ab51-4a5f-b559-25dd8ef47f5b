import React, { useState } from 'react';
import {
  ArrowLeftIcon,
  SaveIcon,
  ClockIcon,
  CalendarIcon,
  GlobeIcon,
  PlusIcon,
  CheckCircleIcon,
  EditIcon,
  TrashIcon,
  BellOffIcon,
} from 'lucide-react';
import { useRouter } from 'next/navigation';
export const BusinessHours: React.FC = () => {
  const navigate = useRouter();
  const [timezone, setTimezone] = useState('america_new_york');
  const [enabledDays, setEnabledDays] = useState({
    monday: true,
    tuesday: true,
    wednesday: true,
    thursday: true,
    friday: true,
    saturday: false,
    sunday: false,
  });
  const [showHolidayModal, setShowHolidayModal] = useState(false);
  const [showScheduleModal, setShowScheduleModal] = useState(false);
  // Sample holiday data
  const holidays = [
    {
      id: 1,
      name: "New Year's Day",
      date: '2024-01-01',
      type: 'global',
    },
    {
      id: 2,
      name: 'Memorial Day',
      date: '2024-05-27',
      type: 'us',
    },
    {
      id: 3,
      name: 'Independence Day',
      date: '2024-07-04',
      type: 'us',
    },
    {
      id: 4,
      name: 'Labor Day',
      date: '2024-09-02',
      type: 'us',
    },
    {
      id: 5,
      name: 'Thanksgiving Day',
      date: '2024-11-28',
      type: 'us',
    },
    {
      id: 6,
      name: 'Christmas Day',
      date: '2024-12-25',
      type: 'global',
    },
    {
      id: 7,
      name: 'Company Retreat',
      date: '2024-08-15',
      type: 'custom',
    },
  ];
  // Sample schedules data
  const schedules = [
    {
      id: 1,
      name: 'Weekday Standard',
      days: 'Monday - Friday',
      hours: '9:00 AM - 5:00 PM',
      timezone: 'America/New_York',
      active: true,
      isDefault: true,
    },
    {
      id: 2,
      name: 'Weekend Limited',
      days: 'Saturday',
      hours: '10:00 AM - 2:00 PM',
      timezone: 'America/New_York',
      active: true,
      isDefault: false,
    },
    {
      id: 3,
      name: 'European Hours',
      days: 'Monday - Friday',
      hours: '3:00 AM - 11:00 AM',
      timezone: 'America/New_York (9:00 AM - 5:00 PM CET)',
      active: true,
      isDefault: false,
    },
    {
      id: 4,
      name: 'Extended Support',
      days: 'Monday - Friday',
      hours: '9:00 AM - 8:00 PM',
      timezone: 'America/New_York',
      active: false,
      isDefault: false,
    },
  ];
  const timezoneOptions = [
    {
      value: 'america_new_york',
      label: 'America/New York (UTC-5)',
    },
    {
      value: 'america_chicago',
      label: 'America/Chicago (UTC-6)',
    },
    {
      value: 'america_denver',
      label: 'America/Denver (UTC-7)',
    },
    {
      value: 'america_los_angeles',
      label: 'America/Los Angeles (UTC-8)',
    },
    {
      value: 'europe_london',
      label: 'Europe/London (UTC+0)',
    },
    {
      value: 'europe_paris',
      label: 'Europe/Paris (UTC+1)',
    },
    {
      value: 'asia_tokyo',
      label: 'Asia/Tokyo (UTC+9)',
    },
    {
      value: 'australia_sydney',
      label: 'Australia/Sydney (UTC+11)',
    },
  ];
  const handleSave = () => {
    // Save logic would go here
    console.log('Business hours settings saved');
  };
  const handleDayToggle = (day: keyof typeof enabledDays) => {
    setEnabledDays((prev) => ({
      ...prev,
      [day]: !prev[day],
    }));
  };
  return (
    <div className="w-full">
      {/* <Breadcrumbs
        items={[
          {
            label: 'Settings',
            path: '/settings',
          },
          {
            label: 'Business Hours',
          },
        ]}
      /> */}
      <div className="flex justify-between items-center mb-6">
        <div className="flex items-center">
          <button
            onClick={() => navigate.push('/settings')}
            className="mr-4 p-2 rounded hover:bg-light-muted dark:hover:bg-dark-muted transition-colors"
          >
            <ArrowLeftIcon size={20} />
          </button>
          <div>
            <h1 className="text-2xl font-semibold text-light-foreground dark:text-dark-foreground">
              Business Hours
            </h1>
            <p className="text-sm text-light-muted-foreground dark:text-dark-muted-foreground">
              Configure working days, hours, timezones, and holidays
            </p>
          </div>
        </div>
        <button
          onClick={handleSave}
          className="flex items-center px-4 py-2 rounded bg-gradient-to-r from-b6ai-cyan via-b6ai-blue to-b6ai-navy text-white transition-colors"
        >
          <SaveIcon size={18} className="mr-2" />
          Save Changes
        </button>
      </div>
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* Primary Business Hours */}
        <div className="lg:col-span-2 bg-light-card dark:bg-dark-card rounded-lg border border-light-border dark:border-dark-border overflow-hidden">
          <div className="border-b border-light-border dark:border-dark-border p-4">
            <div className="flex items-center">
              <ClockIcon
                size={20}
                className="mr-2 text-b6ai-blue dark:text-b6ai-cyan"
              />
              <h2 className="text-lg font-medium">Primary Business Hours</h2>
            </div>
          </div>
          <div className="p-6">
            <div className="flex flex-col md:flex-row md:items-center justify-between gap-4 mb-6">
              <div className="flex-grow">
                <label className="block text-sm font-medium mb-2">
                  Primary Timezone
                </label>
                <select
                  value={timezone}
                  onChange={(e) => setTimezone(e.target.value)}
                  className="w-full px-3 py-2 rounded border border-light-border dark:border-dark-border bg-light-background dark:bg-dark-background focus:outline-none focus:ring-2 focus:ring-b6ai-blue dark:focus:ring-b6ai-cyan"
                >
                  {timezoneOptions.map((option) => (
                    <option key={option.value} value={option.value}>
                      {option.label}
                    </option>
                  ))}
                </select>
                <p className="text-xs text-light-muted-foreground dark:text-dark-muted-foreground mt-1">
                  All times will be displayed in this timezone
                </p>
              </div>
              <div className="flex-shrink-0">
                <div className="flex items-center justify-between mb-2">
                  <label className="block text-sm font-medium">
                    24-Hour Format
                  </label>
                  <label className="relative inline-flex items-center cursor-pointer">
                    <input
                      type="checkbox"
                      defaultChecked={false}
                      className="sr-only peer"
                    />
                    <div className="w-11 h-6 bg-light-muted dark:bg-dark-muted peer-focus:outline-none rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-b6ai-blue dark:peer-checked:bg-b6ai-cyan"></div>
                  </label>
                </div>
                <p className="text-xs text-light-muted-foreground dark:text-dark-muted-foreground">
                  Use 24-hour time format
                </p>
              </div>
            </div>
            <div className="mb-6">
              <h3 className="text-sm font-medium mb-4">Working Days</h3>
              <div className="grid grid-cols-7 gap-2">
                {Object.entries(enabledDays).map(([day, enabled]) => (
                  <button
                    key={day}
                    onClick={() =>
                      handleDayToggle(day as keyof typeof enabledDays)
                    }
                    className={`p-3 rounded-lg text-center transition-colors ${
                      enabled
                        ? 'bg-b6ai-blue dark:bg-b6ai-cyan text-white'
                        : 'bg-light-muted dark:bg-dark-muted text-light-muted-foreground dark:text-dark-muted-foreground'
                    }`}
                  >
                    {day.charAt(0).toUpperCase() + day.slice(1, 3)}
                  </button>
                ))}
              </div>
            </div>
            <div className="mb-6">
              <h3 className="text-sm font-medium mb-4">Working Hours</h3>
              <div className="space-y-4">
                <div className="flex flex-col md:flex-row items-start md:items-center gap-4">
                  <div className="flex items-center">
                    <input
                      type="checkbox"
                      id="monday"
                      className="w-4 h-4 rounded border-light-border dark:border-dark-border focus:ring-b6ai-blue dark:focus:ring-b6ai-cyan"
                      checked={enabledDays.monday}
                      onChange={() => handleDayToggle('monday')}
                    />
                    <label htmlFor="monday" className="ml-2 text-sm w-24">
                      Monday
                    </label>
                  </div>
                  <div className="flex items-center gap-2 flex-1">
                    <input
                      type="time"
                      defaultValue="09:00"
                      className="px-3 py-2 rounded border border-light-border dark:border-dark-border bg-light-background dark:bg-dark-background focus:outline-none focus:ring-2 focus:ring-b6ai-blue dark:focus:ring-b6ai-cyan"
                      disabled={!enabledDays.monday}
                    />
                    <span>to</span>
                    <input
                      type="time"
                      defaultValue="17:00"
                      className="px-3 py-2 rounded border border-light-border dark:border-dark-border bg-light-background dark:bg-dark-background focus:outline-none focus:ring-2 focus:ring-b6ai-blue dark:focus:ring-b6ai-cyan"
                      disabled={!enabledDays.monday}
                    />
                  </div>
                </div>
                <div className="flex flex-col md:flex-row items-start md:items-center gap-4">
                  <div className="flex items-center">
                    <input
                      type="checkbox"
                      id="tuesday"
                      className="w-4 h-4 rounded border-light-border dark:border-dark-border focus:ring-b6ai-blue dark:focus:ring-b6ai-cyan"
                      checked={enabledDays.tuesday}
                      onChange={() => handleDayToggle('tuesday')}
                    />
                    <label htmlFor="tuesday" className="ml-2 text-sm w-24">
                      Tuesday
                    </label>
                  </div>
                  <div className="flex items-center gap-2 flex-1">
                    <input
                      type="time"
                      defaultValue="09:00"
                      className="px-3 py-2 rounded border border-light-border dark:border-dark-border bg-light-background dark:bg-dark-background focus:outline-none focus:ring-2 focus:ring-b6ai-blue dark:focus:ring-b6ai-cyan"
                      disabled={!enabledDays.tuesday}
                    />
                    <span>to</span>
                    <input
                      type="time"
                      defaultValue="17:00"
                      className="px-3 py-2 rounded border border-light-border dark:border-dark-border bg-light-background dark:bg-dark-background focus:outline-none focus:ring-2 focus:ring-b6ai-blue dark:focus:ring-b6ai-cyan"
                      disabled={!enabledDays.tuesday}
                    />
                  </div>
                </div>
                <div className="flex flex-col md:flex-row items-start md:items-center gap-4">
                  <div className="flex items-center">
                    <input
                      type="checkbox"
                      id="wednesday"
                      className="w-4 h-4 rounded border-light-border dark:border-dark-border focus:ring-b6ai-blue dark:focus:ring-b6ai-cyan"
                      checked={enabledDays.wednesday}
                      onChange={() => handleDayToggle('wednesday')}
                    />
                    <label htmlFor="wednesday" className="ml-2 text-sm w-24">
                      Wednesday
                    </label>
                  </div>
                  <div className="flex items-center gap-2 flex-1">
                    <input
                      type="time"
                      defaultValue="09:00"
                      className="px-3 py-2 rounded border border-light-border dark:border-dark-border bg-light-background dark:bg-dark-background focus:outline-none focus:ring-2 focus:ring-b6ai-blue dark:focus:ring-b6ai-cyan"
                      disabled={!enabledDays.wednesday}
                    />
                    <span>to</span>
                    <input
                      type="time"
                      defaultValue="17:00"
                      className="px-3 py-2 rounded border border-light-border dark:border-dark-border bg-light-background dark:bg-dark-background focus:outline-none focus:ring-2 focus:ring-b6ai-blue dark:focus:ring-b6ai-cyan"
                      disabled={!enabledDays.wednesday}
                    />
                  </div>
                </div>
                <div className="flex flex-col md:flex-row items-start md:items-center gap-4">
                  <div className="flex items-center">
                    <input
                      type="checkbox"
                      id="thursday"
                      className="w-4 h-4 rounded border-light-border dark:border-dark-border focus:ring-b6ai-blue dark:focus:ring-b6ai-cyan"
                      checked={enabledDays.thursday}
                      onChange={() => handleDayToggle('thursday')}
                    />
                    <label htmlFor="thursday" className="ml-2 text-sm w-24">
                      Thursday
                    </label>
                  </div>
                  <div className="flex items-center gap-2 flex-1">
                    <input
                      type="time"
                      defaultValue="09:00"
                      className="px-3 py-2 rounded border border-light-border dark:border-dark-border bg-light-background dark:bg-dark-background focus:outline-none focus:ring-2 focus:ring-b6ai-blue dark:focus:ring-b6ai-cyan"
                      disabled={!enabledDays.thursday}
                    />
                    <span>to</span>
                    <input
                      type="time"
                      defaultValue="17:00"
                      className="px-3 py-2 rounded border border-light-border dark:border-dark-border bg-light-background dark:bg-dark-background focus:outline-none focus:ring-2 focus:ring-b6ai-blue dark:focus:ring-b6ai-cyan"
                      disabled={!enabledDays.thursday}
                    />
                  </div>
                </div>
                <div className="flex flex-col md:flex-row items-start md:items-center gap-4">
                  <div className="flex items-center">
                    <input
                      type="checkbox"
                      id="friday"
                      className="w-4 h-4 rounded border-light-border dark:border-dark-border focus:ring-b6ai-blue dark:focus:ring-b6ai-cyan"
                      checked={enabledDays.friday}
                      onChange={() => handleDayToggle('friday')}
                    />
                    <label htmlFor="friday" className="ml-2 text-sm w-24">
                      Friday
                    </label>
                  </div>
                  <div className="flex items-center gap-2 flex-1">
                    <input
                      type="time"
                      defaultValue="09:00"
                      className="px-3 py-2 rounded border border-light-border dark:border-dark-border bg-light-background dark:bg-dark-background focus:outline-none focus:ring-2 focus:ring-b6ai-blue dark:focus:ring-b6ai-cyan"
                      disabled={!enabledDays.friday}
                    />
                    <span>to</span>
                    <input
                      type="time"
                      defaultValue="17:00"
                      className="px-3 py-2 rounded border border-light-border dark:border-dark-border bg-light-background dark:bg-dark-background focus:outline-none focus:ring-2 focus:ring-b6ai-blue dark:focus:ring-b6ai-cyan"
                      disabled={!enabledDays.friday}
                    />
                  </div>
                </div>
                <div className="flex flex-col md:flex-row items-start md:items-center gap-4">
                  <div className="flex items-center">
                    <input
                      type="checkbox"
                      id="saturday"
                      className="w-4 h-4 rounded border-light-border dark:border-dark-border focus:ring-b6ai-blue dark:focus:ring-b6ai-cyan"
                      checked={enabledDays.saturday}
                      onChange={() => handleDayToggle('saturday')}
                    />
                    <label htmlFor="saturday" className="ml-2 text-sm w-24">
                      Saturday
                    </label>
                  </div>
                  <div className="flex items-center gap-2 flex-1">
                    <input
                      type="time"
                      defaultValue="10:00"
                      className="px-3 py-2 rounded border border-light-border dark:border-dark-border bg-light-background dark:bg-dark-background focus:outline-none focus:ring-2 focus:ring-b6ai-blue dark:focus:ring-b6ai-cyan"
                      disabled={!enabledDays.saturday}
                    />
                    <span>to</span>
                    <input
                      type="time"
                      defaultValue="14:00"
                      className="px-3 py-2 rounded border border-light-border dark:border-dark-border bg-light-background dark:bg-dark-background focus:outline-none focus:ring-2 focus:ring-b6ai-blue dark:focus:ring-b6ai-cyan"
                      disabled={!enabledDays.saturday}
                    />
                  </div>
                </div>
                <div className="flex flex-col md:flex-row items-start md:items-center gap-4">
                  <div className="flex items-center">
                    <input
                      type="checkbox"
                      id="sunday"
                      className="w-4 h-4 rounded border-light-border dark:border-dark-border focus:ring-b6ai-blue dark:focus:ring-b6ai-cyan"
                      checked={enabledDays.sunday}
                      onChange={() => handleDayToggle('sunday')}
                    />
                    <label htmlFor="sunday" className="ml-2 text-sm w-24">
                      Sunday
                    </label>
                  </div>
                  <div className="flex items-center gap-2 flex-1">
                    <input
                      type="time"
                      defaultValue="10:00"
                      className="px-3 py-2 rounded border border-light-border dark:border-dark-border bg-light-background dark:bg-dark-background focus:outline-none focus:ring-2 focus:ring-b6ai-blue dark:focus:ring-b6ai-cyan"
                      disabled={!enabledDays.sunday}
                    />
                    <span>to</span>
                    <input
                      type="time"
                      defaultValue="14:00"
                      className="px-3 py-2 rounded border border-light-border dark:border-dark-border bg-light-background dark:bg-dark-background focus:outline-none focus:ring-2 focus:ring-b6ai-blue dark:focus:ring-b6ai-cyan"
                      disabled={!enabledDays.sunday}
                    />
                  </div>
                </div>
              </div>
            </div>
            <div className="mb-6">
              <div className="flex items-center justify-between mb-2">
                <label className="block text-sm font-medium">Lunch Break</label>
                <label className="relative inline-flex items-center cursor-pointer">
                  <input
                    type="checkbox"
                    defaultChecked={true}
                    className="sr-only peer"
                  />
                  <div className="w-11 h-6 bg-light-muted dark:bg-dark-muted peer-focus:outline-none rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-b6ai-blue dark:peer-checked:bg-b6ai-cyan"></div>
                </label>
              </div>
              <div className="flex items-center gap-2 mt-2">
                <input
                  type="time"
                  defaultValue="12:00"
                  className="px-3 py-2 rounded border border-light-border dark:border-dark-border bg-light-background dark:bg-dark-background focus:outline-none focus:ring-2 focus:ring-b6ai-blue dark:focus:ring-b6ai-cyan"
                />
                <span>to</span>
                <input
                  type="time"
                  defaultValue="13:00"
                  className="px-3 py-2 rounded border border-light-border dark:border-dark-border bg-light-background dark:bg-dark-background focus:outline-none focus:ring-2 focus:ring-b6ai-blue dark:focus:ring-b6ai-cyan"
                />
              </div>
              <p className="text-xs text-light-muted-foreground dark:text-dark-muted-foreground mt-1">
                Define lunch break hours
              </p>
            </div>
          </div>
        </div>
        {/* Out of Office Settings */}
        <div className="bg-light-card dark:bg-dark-card rounded-lg border border-light-border dark:border-dark-border overflow-hidden">
          <div className="border-b border-light-border dark:border-dark-border p-4">
            <div className="flex items-center">
              <BellOffIcon
                size={20}
                className="mr-2 text-b6ai-blue dark:text-b6ai-cyan"
              />
              <h2 className="text-lg font-medium">Out of Office Settings</h2>
            </div>
          </div>
          <div className="p-6">
            <div className="mb-6">
              <div className="flex items-center justify-between mb-2">
                <label className="block text-sm font-medium">Auto-Reply</label>
                <label className="relative inline-flex items-center cursor-pointer">
                  <input
                    type="checkbox"
                    defaultChecked={true}
                    className="sr-only peer"
                  />
                  <div className="w-11 h-6 bg-light-muted dark:bg-dark-muted peer-focus:outline-none rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-b6ai-blue dark:peer-checked:bg-b6ai-cyan"></div>
                </label>
              </div>
              <p className="text-xs text-light-muted-foreground dark:text-dark-muted-foreground mb-2">
                Send auto-replies outside business hours
              </p>
            </div>
            <div className="mb-6">
              <div className="flex items-center justify-between mb-2">
                <label className="block text-sm font-medium">
                  Holiday Auto-Reply
                </label>
                <label className="relative inline-flex items-center cursor-pointer">
                  <input
                    type="checkbox"
                    defaultChecked={true}
                    className="sr-only peer"
                  />
                  <div className="w-11 h-6 bg-light-muted dark:bg-dark-muted peer-focus:outline-none rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-b6ai-blue dark:peer-checked:bg-b6ai-cyan"></div>
                </label>
              </div>
              <p className="text-xs text-light-muted-foreground dark:text-dark-muted-foreground mb-2">
                Send auto-replies on holidays
              </p>
            </div>
            <div className="mb-6">
              <label className="block text-sm font-medium mb-2">
                Out of Office Message
              </label>
              <textarea
                defaultValue="Thank you for your message. Our office is currently closed for a holiday. We will respond to your message when we return."
                rows={4}
                className="w-full px-3 py-2 rounded border border-light-border dark:border-dark-border bg-light-background dark:bg-dark-background focus:outline-none focus:ring-2 focus:ring-b6ai-blue dark:focus:ring-b6ai-cyan"
              />
            </div>
            <div className="mb-6">
              <div className="flex items-center justify-between mb-2">
                <label className="block text-sm font-medium">
                  After-Hours Message
                </label>
                <label className="relative inline-flex items-center cursor-pointer">
                  <input
                    type="checkbox"
                    defaultChecked={true}
                    className="sr-only peer"
                  />
                  <div className="w-11 h-6 bg-light-muted dark:bg-dark-muted peer-focus:outline-none rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-b6ai-blue dark:peer-checked:bg-b6ai-cyan"></div>
                </label>
              </div>
              <textarea
                defaultValue="Thank you for contacting us. Our office is currently closed. We will respond to your message during our business hours."
                rows={3}
                className="w-full px-3 py-2 rounded border border-light-border dark:border-dark-border bg-light-background dark:bg-dark-background focus:outline-none focus:ring-2 focus:ring-b6ai-blue dark:focus:ring-b6ai-cyan"
              />
              <p className="text-xs text-light-muted-foreground dark:text-dark-muted-foreground mt-1">
                Message displayed to customers outside of business hours
              </p>
            </div>
            <div className="mb-6">
              <div className="flex items-center justify-between mb-2">
                <label className="block text-sm font-medium">
                  Collect Contact Info
                </label>
                <label className="relative inline-flex items-center cursor-pointer">
                  <input
                    type="checkbox"
                    defaultChecked={true}
                    className="sr-only peer"
                  />
                  <div className="w-11 h-6 bg-light-muted dark:bg-dark-muted peer-focus:outline-none rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-b6ai-blue dark:peer-checked:bg-b6ai-cyan"></div>
                </label>
              </div>
              <p className="text-xs text-light-muted-foreground dark:text-dark-muted-foreground mb-2">
                Collect contact information for follow-up
              </p>
            </div>
            <div className="mb-6">
              <div className="flex items-center justify-between mb-2">
                <label className="block text-sm font-medium">
                  Emergency Contact
                </label>
                <label className="relative inline-flex items-center cursor-pointer">
                  <input
                    type="checkbox"
                    defaultChecked={false}
                    className="sr-only peer"
                  />
                  <div className="w-11 h-6 bg-light-muted dark:bg-dark-muted peer-focus:outline-none rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-b6ai-blue dark:peer-checked:bg-b6ai-cyan"></div>
                </label>
              </div>
              <p className="text-xs text-light-muted-foreground dark:text-dark-muted-foreground mb-2">
                Show emergency contact information
              </p>
              <input
                type="text"
                placeholder="Emergency contact email or phone"
                className="w-full px-3 py-2 rounded border border-light-border dark:border-dark-border bg-light-background dark:bg-dark-background focus:outline-none focus:ring-2 focus:ring-b6ai-blue dark:focus:ring-b6ai-cyan"
              />
            </div>
          </div>
        </div>
        {/* Holiday Schedule */}
        <div className="lg:col-span-3 bg-light-card dark:bg-dark-card rounded-lg border border-light-border dark:border-dark-border overflow-hidden">
          <div className="border-b border-light-border dark:border-dark-border p-4 flex justify-between items-center">
            <div className="flex items-center">
              <CalendarIcon
                size={20}
                className="mr-2 text-b6ai-blue dark:text-b6ai-cyan"
              />
              <h2 className="text-lg font-medium">Holiday Schedule</h2>
            </div>
            <button
              onClick={() => setShowHolidayModal(true)}
              className="px-3 py-1 text-sm bg-b6ai-blue dark:bg-b6ai-cyan text-white rounded inline-flex items-center"
            >
              <PlusIcon size={16} className="mr-1" />
              Add Holiday
            </button>
          </div>
          <div className="p-6">
            <div className="overflow-x-auto">
              <table className="w-full text-sm">
                <thead className="text-xs uppercase bg-light-muted dark:bg-dark-muted">
                  <tr>
                    <th className="px-4 py-3 text-left">Holiday Name</th>
                    <th className="px-4 py-3 text-left">Date</th>
                    <th className="px-4 py-3 text-left">Type</th>
                    <th className="px-4 py-3 text-right">Actions</th>
                  </tr>
                </thead>
                <tbody>
                  {holidays.map((holiday) => (
                    <tr
                      key={holiday.id}
                      className="border-b border-light-border dark:border-dark-border"
                    >
                      <td className="px-4 py-3">{holiday.name}</td>
                      <td className="px-4 py-3">{holiday.date}</td>
                      <td className="px-4 py-3">
                        <span
                          className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
                            holiday.type === 'global'
                              ? 'bg-blue-100 dark:bg-blue-900 text-blue-800 dark:text-blue-200'
                              : holiday.type === 'us'
                              ? 'bg-green-100 dark:bg-green-900 text-green-800 dark:text-green-200'
                              : 'bg-purple-100 dark:bg-purple-900 text-purple-800 dark:text-purple-200'
                          }`}
                        >
                          {holiday.type === 'global'
                            ? 'Global'
                            : holiday.type === 'us'
                            ? 'US'
                            : 'Custom'}
                        </span>
                      </td>
                      <td className="px-4 py-3 text-right">
                        <button className="text-light-muted-foreground dark:text-dark-muted-foreground hover:text-light-foreground dark:hover:text-dark-foreground p-1">
                          <EditIcon size={16} />
                        </button>
                        <button className="text-light-muted-foreground dark:text-dark-muted-foreground hover:text-light-destructive dark:hover:text-dark-destructive p-1 ml-1">
                          <TrashIcon size={16} />
                        </button>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
            <div className="mt-6 flex justify-between items-center">
              <button className="px-4 py-2 text-sm text-b6ai-blue dark:text-b6ai-cyan border border-b6ai-blue dark:border-b6ai-cyan rounded">
                Import Holiday Calendar
              </button>
              <div className="flex items-center">
                <input
                  type="checkbox"
                  id="auto-import"
                  className="w-4 h-4 rounded border-light-border dark:border-dark-border focus:ring-b6ai-blue dark:focus:ring-b6ai-cyan"
                  defaultChecked
                />
                <label htmlFor="auto-import" className="ml-2 text-sm">
                  Auto-import country holidays
                </label>
                <select
                  defaultValue="us"
                  className="ml-2 px-3 py-1 text-sm rounded border border-light-border dark:border-dark-border bg-light-background dark:bg-dark-background focus:outline-none focus:ring-2 focus:ring-b6ai-blue dark:focus:ring-b6ai-cyan"
                >
                  <option value="us">United States</option>
                  <option value="uk">United Kingdom</option>
                  <option value="ca">Canada</option>
                  <option value="au">Australia</option>
                  <option value="de">Germany</option>
                  <option value="fr">France</option>
                </select>
              </div>
            </div>
          </div>
        </div>
        {/* Multiple Business Hours Schedules */}
        <div className="lg:col-span-3 bg-light-card dark:bg-dark-card rounded-lg border border-light-border dark:border-dark-border overflow-hidden">
          <div className="border-b border-light-border dark:border-dark-border p-4 flex justify-between items-center">
            <div className="flex items-center">
              <GlobeIcon
                size={20}
                className="mr-2 text-b6ai-blue dark:text-b6ai-cyan"
              />
              <h2 className="text-lg font-medium">Business Hours Schedules</h2>
            </div>
            <button
              onClick={() => setShowScheduleModal(true)}
              className="px-3 py-1 text-sm bg-b6ai-blue dark:bg-b6ai-cyan text-white rounded inline-flex items-center"
            >
              <PlusIcon size={16} className="mr-1" />
              Add Schedule
            </button>
          </div>
          <div className="p-6">
            <p className="text-sm text-light-muted-foreground dark:text-dark-muted-foreground mb-4">
              Create multiple business hour schedules for different teams,
              departments, or regions.
            </p>
            <div className="overflow-x-auto">
              <table className="w-full text-sm">
                <thead className="text-xs uppercase bg-light-muted dark:bg-dark-muted">
                  <tr>
                    <th className="px-4 py-3 text-left">Schedule Name</th>
                    <th className="px-4 py-3 text-left">Working Days</th>
                    <th className="px-4 py-3 text-left">Working Hours</th>
                    <th className="px-4 py-3 text-left">Timezone</th>
                    <th className="px-4 py-3 text-left">Status</th>
                    <th className="px-4 py-3 text-center">Default</th>
                    <th className="px-4 py-3 text-right">Actions</th>
                  </tr>
                </thead>
                <tbody>
                  {schedules.map((schedule) => (
                    <tr
                      key={schedule.id}
                      className="border-b border-light-border dark:border-dark-border"
                    >
                      <td className="px-4 py-3">{schedule.name}</td>
                      <td className="px-4 py-3">{schedule.days}</td>
                      <td className="px-4 py-3">{schedule.hours}</td>
                      <td className="px-4 py-3">{schedule.timezone}</td>
                      <td className="px-4 py-3">
                        {schedule.active ? (
                          <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 dark:bg-green-900 text-green-800 dark:text-green-200">
                            <CheckCircleIcon size={12} className="mr-1" />{' '}
                            Active
                          </span>
                        ) : (
                          <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-gray-100 dark:bg-gray-800 text-gray-800 dark:text-gray-200">
                            Inactive
                          </span>
                        )}
                      </td>
                      <td className="px-4 py-3 text-center">
                        <input
                          type="radio"
                          name="default-schedule"
                          checked={schedule.isDefault}
                          readOnly
                          className="w-4 h-4 text-b6ai-blue dark:text-b6ai-cyan focus:ring-b6ai-blue dark:focus:ring-b6ai-cyan"
                        />
                      </td>
                      <td className="px-4 py-3 text-right">
                        <button className="text-light-muted-foreground dark:text-dark-muted-foreground hover:text-light-foreground dark:hover:text-dark-foreground p-1">
                          <EditIcon size={16} />
                        </button>
                        <button className="text-light-muted-foreground dark:text-dark-muted-foreground hover:text-light-destructive dark:hover:text-dark-destructive p-1 ml-1">
                          <TrashIcon size={16} />
                        </button>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
            <div className="mt-6">
              <p className="text-sm text-light-muted-foreground dark:text-dark-muted-foreground">
                Note: You can assign different schedules to different teams,
                departments, or routing rules.
              </p>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};
