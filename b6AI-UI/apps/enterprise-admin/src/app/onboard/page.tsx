'use client';

import { useState } from 'react';
import { useRouter } from 'next/navigation';
import { Input, Button } from '@b6ai/ui';
import { CheckCircle, XCircle } from 'lucide-react';
import Image from 'next/image';
import { useToast } from '../context/toast-context';
// ✅ import your toast hook

export default function OnboardPage() {
  const [email, setEmail] = useState('');
  const router = useRouter();
  const toast = useToast(); // ✅ get toast functions

  // Simple email regex validation
  const isValidEmail = (email: string) =>
    /^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(email);

  const isValid = email ? isValidEmail(email) : null;

  const handleSignUp = () => {
    if (isValid) {
      localStorage.setItem('signupEmail', email); // ✅ save email
      router.push('/onboard/email-verification'); // ✅ navigate
    } else {
      toast.error('Please enter a valid email address'); // ✅ show toast instead of alert
    }
  };

  return (
    <div className="min-h-screen flex bg-[white]">
      {/* Left section */}
      <div className="flex-1 flex items-center justify-center p-8">
        <div className="w-full max-w-md space-y-8">
          <div className="text-center space-y-4">
            <h1 className="text-4xl font-bold text-[#041C91] leading-tight">
              <span className="whitespace-nowrap">Connect With Us and</span>{' '}
              <br />
              <span className="whitespace-nowrap">
                Unlock Smarter Interactions
              </span>
            </h1>
          </div>

          <div className="space-y-2 relative">
            <label
              htmlFor="email"
              className="text-sm font-medium text-gray-700"
            >
              Email Address
            </label>
            <Input
              id="email"
              name="email"
              type="email"
              value={email}
              onChange={(e) => setEmail(e.target.value)}
              placeholder="Enter your email"
              className={`w-full h-12 px-4 text-base rounded-lg
                ${
                  isValid === null
                    ? 'border-gray-300 focus:border-gray-300 focus:ring-gray-300'
                    : ''
                } 
                ${
                  isValid === false
                    ? 'border-red-500 focus:border-red-600 focus:ring-red-200'
                    : ''
                }`}
            />
            {isValid === true && (
              <CheckCircle className="absolute right-3 top-1/2 transform -translate-y-1/2 text-green-500 w-5 h-5" />
            )}
            {isValid === false && (
              <XCircle className="absolute right-3 top-1/2 transform -translate-y-1/2 text-red-500 w-5 h-5" />
            )}
          </div>

          <Button
            onClick={handleSignUp}
            className="w-full px-4 py-2 btn-b6ai rounded-md"
          >
            Sign up
          </Button>

          {/* Divider */}
          <div className="flex items-center space-x-4">
            <div className="flex-1 h-px bg-b6ai-gradient-bg "></div>
            <span className="text-sm text-gray-500 font-medium">
              Or continue with
            </span>
            <div className="flex-1 h-px bg-b6ai-gradient-bg "></div>
          </div>

          {/* Social buttons */}
          <div className="flex gap-4">
            {/* Google / Microsoft buttons here */}
          </div>

          {/* Footer */}
          <div className="text-center pt-4">
            <p className="text-sm text-gray-600">
              Trying to access B6AI?{' '}
              <Button
                variant="link"
                className="text-primary p-0 h-auto font-medium hover:bg-transparent"
              >
                Log in
              </Button>
            </p>
          </div>
        </div>
      </div>

      {/* Right section */}
      <div className="flex-1 flex items-center justify-center p-6">
        <div className="text-center text-gray-500">
          <div className="mb-6 flex justify-center">
            <Image
              src="/images/bot.gif"
              alt="B6AI Logo"
              width={600}
              height={120}
              className="object-contain"
            />
          </div>
        </div>
      </div>
    </div>
  );
}
