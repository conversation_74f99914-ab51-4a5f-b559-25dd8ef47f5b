@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  :root {
    /* Light Mode */
    --background: 0 0% 100%;
    --foreground: 234 93% 12%;
    --card: 0 0% 100%;
    --card-foreground: 234 93% 12%;
    --popover: 0 0% 100%;
    --popover-foreground: 234 93% 12%;
    --primary: 231 91% 30%;
    --primary-foreground: 0 0% 100%;
    --secondary: 195 75% 56%;
    --secondary-foreground: 0 0% 100%;
    
    --muted: 195 20% 96%;
    --muted-foreground: 234 40% 40%;
    --accent: 234 93% 12%;
    --accent-foreground: 0 0% 100%;
    --destructive: 0 84% 60%;
    --destructive-foreground: 0 0% 100%;
    --border: 231 30% 90%;
    --input: 231 30% 90%;
    --ring: 231 91% 30%;
    --radius: 0.5rem;
    
    /* B6AI Brand Colors */
    --b6ai-cyan: 195 75% 56%;
    --b6ai-blue: 231 91% 30%;
    --b6ai-navy: 234 93% 12%;
    
    /* Gradient definitions */
    --b6ai-gradient: linear-gradient(135deg, hsl(195 75% 56%), hsl(231 91% 30%), hsl(234 93% 12%));
    --b6ai-gradient-subtle: linear-gradient(135deg, hsl(195 75% 56% / 0.1), hsl(231 91% 30% / 0.1), hsl(234 93% 12% / 0.1));
    --b6ai-gradient-bg: linear-gradient(135deg, hsl(195 75% 56% / 0.05), hsl(231 91% 30% / 0.08), hsl(234 93% 12% / 0.12));
  }
  
  .dark {
    /* Dark Mode */
    --background: 234 93% 12%;
    --foreground: 0 0% 100%;
    --card: 234 60% 15%;
    --card-foreground: 0 0% 100%;
    --popover: 234 60% 15%;
    --popover-foreground: 0 0% 100%;
    --primary: 231 91% 30%;
    --primary-foreground: 0 0% 100%;
    --secondary: 195 75% 56%;
    --secondary-foreground: 234 93% 12%;
    --muted: 234 50% 20%;
    --muted-foreground: 195 20% 70%;
    --accent: 234 50% 25%;
    --accent-foreground: 195 75% 56%;
    --destructive: 0 62% 30%;
    --destructive-foreground: 0 0% 100%;
    --border: 234 40% 25%;
    --input: 234 40% 25%;
    --ring: 231 91% 30%;
    
    /* B6AI Brand Colors (same in dark) */
    --b6ai-cyan: 195 75% 56%;
    --b6ai-blue: 231 91% 30%;
    --b6ai-navy: 234 93% 12%;
    
    /* Gradient definitions for dark mode */
    --b6ai-gradient: linear-gradient(135deg, hsl(195 75% 56%), hsl(231 91% 30%), hsl(234 93% 12%));
    --b6ai-gradient-subtle: linear-gradient(135deg, hsl(195 75% 56% / 0.2), hsl(231 91% 30% / 0.2), hsl(234 93% 12% / 0.2));
    --b6ai-gradient-bg: linear-gradient(135deg, hsl(195 75% 56% / 0.08), hsl(231 91% 30% / 0.12), hsl(234 93% 12% / 0.18));
  }
}

@layer base {
  * {
    @apply border-border;
  }
  body {
    @apply bg-background text-foreground;
  }
}

@layer components {
  /* B6AI Gradient Button */
  .btn-b6ai {
    @apply relative overflow-hidden text-white font-medium transition-all duration-300;
    background: var(--b6ai-gradient);
    background-size: 200% 200%;
    animation: gradient-shift 3s ease infinite !important;
  }
  
  .btn-b6ai:hover {
    @apply shadow-lg scale-105;
    box-shadow: 0 10px 25px -5px hsl(195 75% 56% / 0.3);
  }
  
  .btn-b6ai:active {
    @apply scale-100;
  }
  
  /* B6AI Ghost/Outline Button */
  .btn-b6ai-outline {
    @apply relative border-2 bg-transparent transition-all duration-300;
    border-image: var(--b6ai-gradient) 1;
    color: hsl(var(--primary));
  }
  
  .btn-b6ai-outline:hover {
    @apply shadow-md;
    background: var(--b6ai-gradient-subtle);
    color: hsl(var(--primary-foreground));
  }
  
  .dark .btn-b6ai-outline {
    color: hsl(var(--secondary));
  }
  
  .dark .btn-b6ai-outline:hover {
    background: var(--b6ai-gradient-subtle);
    color: hsl(var(--primary));
  }
  
  /* B6AI Background Gradient */
  .bg-b6ai-gradient {
    background: var(--b6ai-gradient-bg);
  }
  
  .bg-b6ai-gradient-animated {
    background: var(--b6ai-gradient);
    background-size: 400% 400%;
    animation: gradient-shift 15s ease infinite;
  }
  
  /* Text Gradient */
  .text-b6ai-gradient {
    @apply bg-clip-text text-transparent;
    background: var(--b6ai-gradient);
    background-size: 200% 200%;
    animation: gradient-shift 5s ease infinite !important;
  }
  
  /* Fix for outlined button hover text visibility */
  .btn-outline,
  button[class*="outline"] {
    @apply transition-colors duration-200;
  }
  
  /* Light mode outline button */
  .btn-outline:hover,
  button[class*="outline"]:hover {
    @apply bg-primary text-primary-foreground;
  }
  
  /* Dark mode outline button - fixed text color */
  .dark .btn-outline,
  .dark button[class*="outline"] {
    @apply text-secondary;
  }
  
  .dark .btn-outline:hover,
  .dark button[class*="outline"]:hover {
    @apply bg-primary text-primary-foreground;
  }
}

@layer utilities {
  /* Gradient animation keyframes */
  @keyframes gradient-shift {
    0% {
      background-position: 0% 50%;
    }
    50% {
      background-position: 100% 50%;
    }
    100% {
      background-position: 0% 50%;
    }
  }
  
  /* Additional utility classes */
  .border-b6ai-gradient {
    border-image: var(--b6ai-gradient) 1 !important;
  }
  
  .shadow-b6ai {
    box-shadow: 0 4px 20px -2px hsl(195 75% 56% / 0.25);
  }
  
  .shadow-b6ai-lg {
    box-shadow: 0 10px 40px -5px hsl(195 75% 56% / 0.35);
  }

  .page-x-padding {
    @apply px-4 sm:px-6 md:px-8 lg:px-12 xl:px-16;
  }
   .page-top-padding {
    @apply pt-4 sm:pt-6 md:pt-8 lg:pt-12 xl:pt-16;
  }
}