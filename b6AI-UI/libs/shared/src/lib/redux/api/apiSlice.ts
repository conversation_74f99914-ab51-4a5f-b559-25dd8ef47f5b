import { createApi, fetchBaseQuery } from '@reduxjs/toolkit/query/react';
import { ApiTags } from '../../types/api-tags.type';
import { RootState } from '../store';

export const apiSlice = createApi({
  reducerPath: 'api',
  baseQuery: fetchBaseQuery({
    baseUrl: 'http://localhost:4001',
    prepareHeaders: (headers, { getState }) => {
      const token = (getState() as RootState).auth?.token;
      if (token) {
        headers.set('authorization', `Bearer ${token}`);
      }
      headers.set('authorization', `Bearer test-token`);
      return headers;
    },
  }),
  tagTypes: Object.values(ApiTags),
  endpoints: (builder) => ({}),
});
